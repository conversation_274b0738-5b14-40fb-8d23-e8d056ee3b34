import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, Trophy, TrendingUp, Star, Calendar, Target, Crown, Medal, Award } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth.tsx";
import { useIdeas } from "@/hooks/use-ideas";
import { useIndustries } from "@/hooks/use-industries";
import SEOHead from "@/components/seo-head";
import ParticleBackground from "@/components/particle-background";
import IdeaGrid from "@/components/idea-grid";
import type { Industry, StartupIdea } from "@/lib/types";

// Category configurations
const CATEGORY_CONFIGS = {
  'ai-startup-ideas-2025': {
    title: 'Best AI Startup Ideas 2025',
    description: 'Top AI and machine learning startup opportunities discovered from Reddit communities. These ideas represent the most promising AI business ventures with high market potential.',
    industryFilter: 'AI & Machine Learning',
    keywords: ['AI startup ideas', 'machine learning business', 'AI opportunities 2025', 'artificial intelligence startup'],
    icon: '🤖'
  },
  'saas-business-opportunities': {
    title: 'Best SaaS Business Opportunities',
    description: 'Top Software-as-a-Service startup ideas with proven market demand. Discover cloud-based business opportunities with recurring revenue potential.',
    industryFilter: 'SaaS & Cloud Services',
    keywords: ['SaaS startup ideas', 'cloud business opportunities', 'software startup', 'SaaS business model'],
    icon: '☁️'
  },
  'startup-ideas-2025': {
    title: 'Best Startup Ideas 2025',
    description: 'The ultimate ranking of the most promising startup opportunities for 2025. Curated from thousands of Reddit discussions and validated by AI analysis.',
    industryFilter: null, // All industries
    keywords: ['startup ideas 2025', 'best business ideas', 'startup opportunities', 'business ideas 2025'],
    icon: '🚀'
  },
  'profitable-business-ideas': {
    title: 'Most Profitable Business Ideas',
    description: 'High-profit potential business ideas with proven market demand and clear monetization strategies. Focus on ideas with strong revenue potential.',
    industryFilter: null,
    keywords: ['profitable business ideas', 'high profit startup', 'money making ideas', 'profitable startup'],
    icon: '💰'
  }
};

export default function BestIdeas() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();

  const category = params.category;
  const config = CATEGORY_CONFIGS[category as keyof typeof CATEGORY_CONFIGS];

  // Get all industries to find the one matching the filter
  const { data: industries } = useIndustries();
  const targetIndustry = config?.industryFilter 
    ? industries?.find(ind => ind.name === config.industryFilter)
    : null;

  // Get ideas based on category
  const { data: ideasData, isLoading: ideasLoading } = useIdeas({
    industryId: targetIndustry?.id,
    sortBy: 'upvotes',
    pageSize: 50
  });

  // Get category stats
  const { data: categoryStats } = useQuery({
    queryKey: ['category-stats', category, targetIndustry?.id],
    queryFn: async () => {
      const { supabase } = await import('@/lib/queryClient');
      
      let query = supabase.from('startup_ideas').select('*', { count: 'exact', head: true });
      
      if (targetIndustry?.id) {
        query = query.eq('industry_id', targetIndustry.id);
      }

      const { count: totalIdeas } = await query;

      // Get average upvotes for this category
      let upvotesQuery = supabase.from('startup_ideas').select('upvotes');
      if (targetIndustry?.id) {
        upvotesQuery = upvotesQuery.eq('industry_id', targetIndustry.id);
      }

      const { data: upvotesData } = await upvotesQuery;
      const avgUpvotes = upvotesData && upvotesData.length > 0 
        ? Math.round(upvotesData.reduce((sum, item) => sum + (item.upvotes || 0), 0) / upvotesData.length)
        : 0;

      // Get top performing idea
      let topIdeaQuery = supabase
        .from('startup_ideas')
        .select('title, upvotes')
        .order('upvotes', { ascending: false })
        .limit(1);
      
      if (targetIndustry?.id) {
        topIdeaQuery = topIdeaQuery.eq('industry_id', targetIndustry.id);
      }

      const { data: topIdea } = await topIdeaQuery;

      return {
        totalIdeas: totalIdeas || 0,
        avgUpvotes,
        topIdea: topIdea?.[0] || null
      };
    },
    enabled: !!config
  });

  // Handle 404 if category not found
  useEffect(() => {
    if (!config) {
      setLocation('/404');
    }
  }, [config, setLocation]);

  if (!config) {
    return null;
  }

  if (ideasLoading) {
    return (
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden min-h-screen">
        <ParticleBackground />
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto space-y-8">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-10 w-10 rounded-lg bg-white/10" />
              <Skeleton className="h-6 w-20 bg-white/10" />
            </div>
            <Skeleton className="h-12 w-3/4 bg-white/10" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Skeleton className="h-32 bg-white/10" />
              <Skeleton className="h-32 bg-white/10" />
              <Skeleton className="h-32 bg-white/10" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  const getRankIcon = (index: number) => {
    if (index === 0) return <Crown className="w-6 h-6 text-yellow-400" />;
    if (index === 1) return <Medal className="w-6 h-6 text-gray-300" />;
    if (index === 2) return <Award className="w-6 h-6 text-amber-600" />;
    return <span className="w-6 h-6 flex items-center justify-center text-lg font-bold text-gray-400">#{index + 1}</span>;
  };

  return (
    <>
      <SEOHead 
        title={`${config.title} | IdeaHunter`}
        description={config.description}
        keywords={config.keywords}
        type="website"
      />
      
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden min-h-screen">
        <ParticleBackground />
        
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <Button
                onClick={() => setLocation('/')}
                variant="ghost"
                className="mb-6 text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>

              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 rounded-xl bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center">
                  <Trophy className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
                    {config.title}
                  </h1>
                  <p className="text-xl text-gray-300">
                    Ranked by Community Validation & Market Potential
                  </p>
                </div>
              </div>

              <p className="text-lg text-gray-300 leading-relaxed max-w-4xl">
                {config.description}
              </p>
            </motion.div>

            {/* Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
            >
              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <Trophy className="w-8 h-8 text-yellow-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {categoryStats?.totalIdeas || 0}
                      </p>
                      <p className="text-gray-400">Total Ideas</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <TrendingUp className="w-8 h-8 text-green-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {categoryStats?.avgUpvotes || 0}
                      </p>
                      <p className="text-gray-400">Avg Upvotes</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <Star className="w-8 h-8 text-purple-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {categoryStats?.topIdea?.upvotes || 0}
                      </p>
                      <p className="text-gray-400">Top Idea Score</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Winner Spotlight */}
            {categoryStats?.topIdea && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="mb-8"
              >
                <Card className="glass-card border-yellow-400/30 bg-gradient-to-r from-yellow-400/10 to-orange-500/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Crown className="w-6 h-6 mr-2 text-yellow-400" />
                      #1 Most Validated Idea
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2">
                          {categoryStats.topIdea.title}
                        </h3>
                        <div className="flex items-center space-x-4">
                          <Badge className="bg-yellow-400/20 text-yellow-400 border-yellow-400/30">
                            {categoryStats.topIdea.upvotes} upvotes
                          </Badge>
                          <Badge className="bg-green-400/20 text-green-400 border-green-400/30">
                            Community Validated
                          </Badge>
                        </div>
                      </div>
                      <Trophy className="w-12 h-12 text-yellow-400" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Ideas Ranking */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white flex items-center">
                  <Trophy className="w-6 h-6 mr-2 text-yellow-400" />
                  Top Ranked Ideas
                </h2>
                {!user && (
                  <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                    Sign in to see all ideas
                  </Badge>
                )}
              </div>

              {/* Custom ranked grid */}
              <div className="space-y-4">
                {(ideasData?.ideas || []).slice(0, 10).map((idea, index) => (
                  <motion.div
                    key={idea.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * index }}
                    className="glass-card border-white/10 p-6 hover:border-white/20 transition-all duration-200 cursor-pointer"
                    onClick={() => setLocation(`/idea/${idea.id}/${idea.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}`)}
                  >
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        {getRankIcon(index)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
                              {idea.title}
                            </h3>
                            <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                              {idea.summary}
                            </p>
                            <div className="flex items-center space-x-4 text-sm">
                              <div className="flex items-center space-x-1">
                                <TrendingUp className="w-4 h-4 text-green-400" />
                                <span className="text-green-400">{idea.upvotes} upvotes</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Target className="w-4 h-4 text-blue-400" />
                                <span className="text-blue-400">{idea.industry?.name}</span>
                              </div>
                              {idea.keywords && idea.keywords.length > 0 && (
                                <Badge className="bg-purple-400/20 text-purple-400 text-xs">
                                  {idea.keywords[0]}
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className="flex-shrink-0 ml-4">
                            <Badge 
                              className={`${
                                index < 3 
                                  ? 'bg-yellow-400/20 text-yellow-400 border-yellow-400/30' 
                                  : 'bg-gray-600/20 text-gray-400 border-gray-600/30'
                              }`}
                            >
                              Rank #{index + 1}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Show more ideas in regular grid */}
              {(ideasData?.ideas || []).length > 10 && (
                <div className="mt-8">
                  <h3 className="text-xl font-bold text-white mb-6">More Great Ideas</h3>
                  <IdeaGrid
                    ideas={(ideasData?.ideas || []).slice(10)}
                    isLoading={false}
                    isLimited={ideasData?.isLimited}
                    useNavigation={true}
                    isFetching={false}
                  />
                </div>
              )}
            </motion.div>

          </div>
        </div>
      </div>
    </>
  );
}

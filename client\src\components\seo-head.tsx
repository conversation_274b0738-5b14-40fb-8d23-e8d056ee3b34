import { useEffect } from 'react';
import type { StartupIdea } from '@/lib/types';
import { getIdeaShareUrl } from '@/lib/url-utils';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  idea?: StartupIdea;
}

export default function SEOHead({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  idea
}: SEOHeadProps) {
  useEffect(() => {
    // Generate SEO data from idea if provided
    let seoTitle = title;
    let seoDescription = description;
    let seoKeywords = keywords;
    let seoUrl = url;
    let seoImage = image;

    if (idea) {
      seoTitle = `${idea.title} | IdeaHunter`;
      seoDescription = idea.summary.length > 160
        ? `${idea.summary.substring(0, 157)}...`
        : idea.summary;
      seoKeywords = [...idea.keywords, (idea.industry as any)?.name || 'startup'];
      seoUrl = getIdeaShareUrl(idea.id, idea.title);
      // Ensure image URL is absolute for social media sharing
      const defaultImage = '/og-image-default.jpg';
      const imageUrl = image || defaultImage;
      seoImage = imageUrl.startsWith('http') ? imageUrl : `${window.location.origin}${imageUrl}`;
    }

    // Update document title
    if (seoTitle) {
      document.title = seoTitle;
    }

    // Update meta tags
    updateMetaTag('description', seoDescription || '');
    updateMetaTag('keywords', seoKeywords.join(', '));
    
    // Open Graph tags
    updateMetaProperty('og:title', seoTitle || '');
    updateMetaProperty('og:description', seoDescription || '');
    updateMetaProperty('og:type', type);
    updateMetaProperty('og:url', seoUrl || window.location.href);
    updateMetaProperty('og:site_name', 'IdeaHunter');
    if (seoImage) {
      updateMetaProperty('og:image', seoImage);
      updateMetaProperty('og:image:width', '1200');
      updateMetaProperty('og:image:height', '630');
      updateMetaProperty('og:image:alt', seoTitle || 'IdeaHunter Startup Idea');
    }

    // Twitter Card tags
    updateMetaProperty('twitter:title', seoTitle || '');
    updateMetaProperty('twitter:description', seoDescription || '');
    updateMetaProperty('twitter:card', 'summary_large_image');
    if (seoImage) {
      updateMetaProperty('twitter:image', seoImage);
    }

    // Structured data for startup ideas
    if (idea) {
      updateStructuredData(idea);
    }

    // Cleanup function to restore default meta tags when component unmounts
    return () => {
      // Restore default title
      document.title = 'IdeaHunter - AI-Powered Reddit Trend Discovery';
      
      // Restore default meta tags
      updateMetaTag('description', 'Discover trending startup opportunities from Reddit communities using AI-powered analysis.');
      updateMetaTag('keywords', 'startup ideas, reddit scraper, trend analysis, entrepreneurship, AI analysis');
      
      updateMetaProperty('og:title', 'IdeaHunter - AI-Powered Reddit Trend Discovery');
      updateMetaProperty('og:description', 'Discover trending startup opportunities from Reddit communities using AI-powered analysis.');
      updateMetaProperty('og:type', 'website');
      
      updateMetaProperty('twitter:title', 'IdeaHunter - AI-Powered Reddit Trend Discovery');
      updateMetaProperty('twitter:description', 'Discover trending startup opportunities from Reddit communities using AI-powered analysis.');
      
      // Remove structured data
      removeStructuredData();
    };
  }, [title, description, keywords, image, url, type, idea]);

  return null; // This component doesn't render anything
}

function updateMetaTag(name: string, content: string) {
  let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.name = name;
    document.head.appendChild(meta);
  }
  meta.content = content;
}

function updateMetaProperty(property: string, content: string) {
  let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute('property', property);
    document.head.appendChild(meta);
  }
  meta.content = content;
}

function updateStructuredData(idea: StartupIdea) {
  // Remove existing structured data
  removeStructuredData();

  // Create new structured data
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": idea.title,
    "description": idea.summary,
    "author": {
      "@type": "Organization",
      "name": "IdeaHunter"
    },
    "publisher": {
      "@type": "Organization",
      "name": "IdeaHunter"
    },
    "datePublished": idea.createdAt,
    "dateModified": idea.updatedAt,
    "keywords": idea.keywords.join(', '),
    "about": {
      "@type": "Thing",
      "name": (idea.industry as any)?.name || 'Startup Idea'
    },
    "url": getIdeaShareUrl(idea.id, idea.title)
  };

  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.id = 'structured-data-idea';
  script.textContent = JSON.stringify(structuredData);
  document.head.appendChild(script);
}

function removeStructuredData() {
  const existingScript = document.getElementById('structured-data-idea');
  if (existingScript) {
    existingScript.remove();
  }
}

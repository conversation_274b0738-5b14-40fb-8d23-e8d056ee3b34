import { Switch, Route, Redirect, Router } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider, useAuth } from "@/hooks/use-auth.tsx";
import NotFound from "@/pages/not-found";
import Dashboard from "@/pages/dashboard";
import IdeaDetailPage from "@/pages/idea-detail";
import Admin from "@/pages/admin";
import AuthCallback from "@/pages/auth-callback";
import PaymentSuccess from "@/pages/payment-success";
import IndustryOverview from "@/pages/industry-overview";
import BestIdeas from "@/pages/best-ideas";
import FAQ from "@/pages/faq";
import StartupValidationGuide from "@/pages/startup-validation-guide";
import Trends from "@/pages/trends";
import CompanyPages from "@/pages/company-pages";
import Footer from "@/components/footer";
import { Analytics } from "@vercel/analytics/react";

function AdminRoute() {
  const { user, isAdmin, loading } = useAuth();
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-white/20 border-t-white rounded-full animate-spin" />
      </div>
    );
  }
  
  // Redirect non-admin users to home page
  if (!user || !isAdmin) {
    return <Redirect to="/" />;
  }
  
  return <Admin />;
}

function AppRouter() {
  // Remove GitHub Pages base path - not needed for Vercel
  const basePath = '';

  return (
    <Router base={basePath}>
      <div className="min-h-screen flex flex-col">
        <div className="flex-1">
          <Switch>
            <Route path="/" component={Dashboard} />
            <Route path="/dashboard" component={Dashboard} />
            <Route path="/dashboard/industry/:industryId" component={Dashboard} />
            <Route path="/dashboard/favorites" component={Dashboard} />
            <Route path="/idea/:id/:slug?" component={IdeaDetailPage} />

            {/* Industry pages */}
            <Route path="/industry/:slug" component={IndustryOverview} />

            {/* Best ideas pages */}
            <Route path="/best/:category" component={BestIdeas} />

            {/* FAQ pages */}
            <Route path="/faq" component={FAQ} />
            <Route path="/faq/how-to-validate-startup-ideas" component={StartupValidationGuide} />

            {/* Trends pages */}
            <Route path="/trends/:year?" component={Trends} />

            {/* Company pages */}
            <Route path="/about" component={() => <CompanyPages params={{ page: 'about' }} />} />
            <Route path="/contact" component={() => <CompanyPages params={{ page: 'contact' }} />} />
            <Route path="/privacy" component={() => <CompanyPages params={{ page: 'privacy' }} />} />
            <Route path="/terms" component={() => <CompanyPages params={{ page: 'terms' }} />} />

            {/* Admin and auth */}
            <Route path="/admin" component={AdminRoute} />
            <Route path="/auth/callback" component={AuthCallback} />
            <Route path="/payment-success" component={PaymentSuccess} />

            <Route component={NotFound} />
          </Switch>
        </div>
        <Footer />
      </div>
    </Router>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <AppRouter />
          <Analytics />
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;

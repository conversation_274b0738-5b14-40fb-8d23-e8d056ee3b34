import { useEffect, useState } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { motion } from "framer-motion";
import { ArrowLeft, TrendingUp, Users, Calendar, Target, Lightbulb, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth.tsx";
import { useIdeas } from "@/hooks/use-ideas";
import { useIndustries } from "@/hooks/use-industries";
import SEOHead from "@/components/seo-head";
import ParticleBackground from "@/components/particle-background";
import IdeaGrid from "@/components/idea-grid";
import type { Industry, StartupIdea } from "@/lib/types";

export default function IndustryOverview() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();

  const industrySlug = params.slug;

  // Get all industries to find the one matching the slug
  const { data: industries, isLoading: industriesLoading } = useIndustries();
  const industry = industries?.find(ind => ind.slug === industrySlug);

  // Get ideas for this industry
  const { data: ideasData, isLoading: ideasLoading } = useIdeas({
    industryId: industry?.id,
    sortBy: 'upvotes',
    pageSize: 50
  });

  // Get industry stats
  const { data: industryStats } = useQuery({
    queryKey: ['industry-stats', industry?.id],
    queryFn: async () => {
      if (!industry?.id) return null;
      
      const { supabase } = await import('@/lib/queryClient');
      
      // Get total ideas count
      const { count: totalIdeas } = await supabase
        .from('startup_ideas')
        .select('*', { count: 'exact', head: true })
        .eq('industry_id', industry.id);

      // Get average upvotes
      const { data: upvotesData } = await supabase
        .from('startup_ideas')
        .select('upvotes')
        .eq('industry_id', industry.id);

      const avgUpvotes = upvotesData && upvotesData.length > 0 
        ? Math.round(upvotesData.reduce((sum, item) => sum + (item.upvotes || 0), 0) / upvotesData.length)
        : 0;

      // Get top subreddits
      const { data: subredditData } = await supabase
        .from('startup_ideas')
        .select('subreddit')
        .eq('industry_id', industry.id);

      const subredditCounts = subredditData?.reduce((acc, item) => {
        acc[item.subreddit] = (acc[item.subreddit] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      const topSubreddits = Object.entries(subredditCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([subreddit, count]) => ({ subreddit, count }));

      // Get top keywords
      const { data: keywordsData } = await supabase
        .from('startup_ideas')
        .select('keywords')
        .eq('industry_id', industry.id);

      const allKeywords = keywordsData?.flatMap(item => item.keywords || []) || [];
      const keywordCounts = allKeywords.reduce((acc, keyword) => {
        acc[keyword] = (acc[keyword] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const topKeywords = Object.entries(keywordCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([keyword, count]) => ({ keyword, count }));

      return {
        totalIdeas: totalIdeas || 0,
        avgUpvotes,
        topSubreddits,
        topKeywords
      };
    },
    enabled: !!industry?.id
  });

  // Handle 404 if industry not found
  useEffect(() => {
    if (!industriesLoading && industries && !industry) {
      setLocation('/404');
    }
  }, [industriesLoading, industries, industry, setLocation]);

  if (industriesLoading || !industry) {
    return (
      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden min-h-screen">
        <ParticleBackground />
        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto space-y-8">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-10 w-10 rounded-lg bg-white/10" />
              <Skeleton className="h-6 w-20 bg-white/10" />
            </div>
            <Skeleton className="h-12 w-3/4 bg-white/10" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Skeleton className="h-32 bg-white/10" />
              <Skeleton className="h-32 bg-white/10" />
              <Skeleton className="h-32 bg-white/10" />
            </div>
          </div>
        </div>
      </div>
    );
  }



  return (
    <>
      <SEOHead
        title={`${industry.name} Startup Ideas 2025 | IdeaHunter`}
        description={`Discover ${industryStats?.totalIdeas || 0}+ trending ${industry.name.toLowerCase()} startup opportunities from Reddit. AI-analyzed business ideas with market insights and validation data.`}
        keywords={[
          `${industry.name.toLowerCase()} startup ideas`,
          `${industry.name.toLowerCase()} business opportunities`,
          'startup ideas 2025',
          'reddit startup ideas',
          'AI startup analysis',
          ...(industryStats?.topKeywords?.slice(0, 5).map(k => k.keyword) || [])
        ]}
        type="website"
      />

      <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative min-h-screen">
        <ParticleBackground />

        <div className="relative z-10 container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">

            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <Button
                onClick={() => setLocation('/')}
                variant="ghost"
                className="mb-6 text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>

              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 rounded-xl flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600">
                  <i className={`${industry.icon} text-2xl text-white`}></i>
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
                    {industry.name}
                  </h1>
                  <p className="text-xl text-gray-300">
                    Startup Ideas & Business Opportunities
                  </p>
                </div>
              </div>

              {industry.description && (
                <p className="text-lg text-gray-300 leading-relaxed max-w-4xl">
                  {industry.description}
                </p>
              )}
            </motion.div>

            {/* Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
            >
              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <Lightbulb className="w-8 h-8 text-yellow-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {industryStats?.totalIdeas || 0}
                      </p>
                      <p className="text-gray-400">Startup Ideas</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <TrendingUp className="w-8 h-8 text-green-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {industryStats?.avgUpvotes || 0}
                      </p>
                      <p className="text-gray-400">Avg Upvotes</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-card border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <Users className="w-8 h-8 text-blue-400" />
                    <div>
                      <p className="text-2xl font-bold text-white">
                        {industryStats?.topSubreddits?.length || 0}
                      </p>
                      <p className="text-gray-400">Active Communities</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Top Keywords */}
            {industryStats?.topKeywords && industryStats.topKeywords.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="mb-8"
              >
                <Card className="glass-card border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Target className="w-5 h-5 mr-2 text-purple-400" />
                      Trending Keywords
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {industryStats.topKeywords.map((item, index) => (
                        <Badge
                          key={item.keyword}
                          className="bg-blue-500/20 text-blue-400 border-blue-500/30 px-3 py-1 text-sm font-medium"
                        >
                          {item.keyword} ({item.count})
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Ideas Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">
                  Top Startup Ideas
                </h2>
                {!user && (
                  <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                    Sign in to see all ideas
                  </Badge>
                )}
              </div>

              <IdeaGrid
                ideas={ideasData?.ideas || []}
                isLoading={ideasLoading}
                isLimited={ideasData?.isLimited}
                useNavigation={true}
                isFetching={ideasLoading}
              />
            </motion.div>

            {/* Top Subreddits */}
            {industryStats?.topSubreddits && industryStats.topSubreddits.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mb-8"
              >
                <Card className="glass-card border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Users className="w-5 h-5 mr-2 text-blue-400" />
                      Top Communities
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {industryStats.topSubreddits.map((item, index) => (
                        <div 
                          key={item.subreddit}
                          className="flex items-center justify-between p-3 rounded-lg bg-white/5 border border-white/10"
                        >
                          <div className="flex items-center space-x-2">
                            <span className="text-gray-400">r/</span>
                            <span className="text-white font-medium">{item.subreddit}</span>
                          </div>
                          <Badge className="bg-gray-700 text-gray-300">
                            {item.count} ideas
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

          </div>
        </div>
      </div>
    </>
  );
}

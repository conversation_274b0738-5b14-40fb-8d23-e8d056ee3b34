{"buildCommand": "npm run build:full", "outputDirectory": "dist", "installCommand": "npm ci", "framework": null, "rewrites": [{"source": "/sitemap.xml", "destination": "/sitemap.xml"}, {"source": "/robots.txt", "destination": "/robots.txt"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}